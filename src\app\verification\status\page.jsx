"use client";
import React, { useEffect, useState } from "react";
import { Icon } from "@iconify/react";
import styles from "./status.module.css";
import { annualIncome } from "@/app/helpers/contants";
import Login from "@/app/sign/login/page";
import { useRouter } from "next/navigation";
import {
  userVerificationStatusApi,
  getstatusDetailsApi,
} from "@/app/api/UserRegistration/userRegistartion";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { generateKycLink, checkKycStatus } from "@/app/api/manualKyc/manualKYC";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { useCheckStatusApihook } from "@/app/hooks/checkKycStatushook";

const Status = () => {
  const router = useRouter();
  const [annualIncomeUser, setAnnualIncomeUser] = useState("");
  const [govtTaxId, setGovtTaxId] = useState("");
  const [primaryBank, setPrimaryBank] = useState("");
  const [otherFinancialProviders, setOtherFinancialProviders] = useState("");
  const [otherproviders, setOtherProvider] = useState("");
  const [loading, setLoading] = useState(false);
  const [buttonDisabled, setButtonDisabled] = useState(false);
  const [kycVerified, setKycVerified] = useState(false);
  const [kycVerificationFailed, setKycVerificationFailed] = useState(null);
  const [alreadyGenerated, setAlreadyGenerated] = useState(null);
  const [kycUrl, setKycUrl] = useState(null);

  let verificationStatus;
  let userId;
  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
    userId = localStorage.getItem("userID");
    verificationStatus = localStorage.getItem("verificationStatus");
  }

  if (verificationStatus === "Document-Verifications") {
    useCheckStatusApihook();
  }

  if (!token) {
    router.push("/sign/login");
  }

  const handleGovtTaxId = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    setGovtTaxId(inputValue);
  };
  const handlePrimaryBankname = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    setPrimaryBank(inputValue);
  };

  const handleOtherProvider = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    setOtherProvider(inputValue);
  };

  const fetchDetailsApi = async () => {
    try {
      const res = await customFetchWithToken.get("/get-document/");

      setAnnualIncomeUser(res.data.data.annual_income_level);
      setGovtTaxId(res.data.data.government_tax_id_number);
      setPrimaryBank(res.data.data.name_of_primary_bank);
      setOtherFinancialProviders(
        res.data.data.other_financial_service_providers
      );
      setKycVerificationFailed(res.data.data.cleardil_status);
    } catch (error) {
      console.log(error);
    }
  };

  const formData = new FormData();
  formData.append("user_id", userId);
  formData.append("flag", "financial");
  formData.append("annual_income_level", annualIncomeUser);
  formData.append("government_tax_id_number", govtTaxId);
  formData.append("name_of_primary_bank", primaryBank);
  formData.append("other_financial_service_providers", otherFinancialProviders);
  formData.append("other_providers", otherproviders);

  const handleUserStatusDocsSubmit = async () => {
    const requiredFields = {
      annualIncomeUser: "Annual income level*",
      govtTaxId: "Government Tax ID number",
      primaryBank: "Name of Primary Bank*",
      otherFinancialProviders: "Other Financial service Providers*",
    };

    for (const [field, label] of Object.entries(requiredFields)) {
      if (!eval(field)) {
        toast.error(`${label} is required.`);
        return false;
      }
    }

    setLoading(true);
    setButtonDisabled(true); // Disable the button
    try {
      toast.warn("Sending");

      const res = await customFetchWithToken.post(
        "/upload-document/",
        formData
      );
      if (res.status === 200) {
        toast.success(res.data.message);
        setTimeout(function () {
          router.push("/pages/searchads");
        }, 1500);
      }
    } catch (error) {
      console.error(error);
      setLoading(false);
      setButtonDisabled(false);
      if (error.message === "Network Error") {
        toast.error("Network Error");
      }
      toast.error(error.response.data.message);
    } finally {
      setLoading(false);
      setButtonDisabled(false);
    }
  };

  const handleKyc = async () => {
    try {
      const res = await customFetchWithToken.post("/kyc-url-generate/");

      setKycUrl(res.data.data);
      setAlreadyGenerated(res.data.message);
      if (typeof window !== "undefined") {
        window.location.href = res.data.data;
      }
      // window.open(res.data.data, "_blank");
    } catch (error) {
      console.log(error);
    }
  };

  const handleKycAndDataFields = async () => {
    const res = await handleUserStatusDocsSubmit();

    if (res !== false) {
      setTimeout(() => {
        handleKyc();
      }, 100);
    }
  };

  const checkKycStatusFunc = async () => {
    try {
      const res = await customFetchWithToken.get("/kyc-status-check/");

      // toast.success(res.data.message);
      if (res.data.kyc_error_status.length > 1) {
        toast.success(res.data.kyc_error_status);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    fetchDetailsApi();
  }, []);

  useEffect(() => {
    checkKycStatusFunc();
  }, []);

  return (
    <>
      {/* {authToken ? ( */}
      <div className={styles.mainContainer}>
        <div className={styles.topContainer}>
          <div className={styles.mobileHeader}>
            <div className={styles.verificationIcon}>
              <Icon icon="mdi:file-document-check" width="48" height="48" />
            </div>
            <h2 className={styles.mobileTitle}>Financial Verification</h2>
          </div>
        </div>
        <div className={styles.leftContainer}>
          <div className={styles.leftWrapper}>
            <div
              className={styles.backBtn}
              onClick={() => router.push("/verification/personaldetails")}
            >
              <Icon icon="mdi:arrow-left" width="24" height="24" />
              <div>Back</div>
            </div>
            <div className={styles.silverWrapper}>
              <div className={styles.formContainer}>
                <form action="">
                  <div className={styles.formWrapper}>
                    <div className={styles.firstName}>
                      <div className={styles.firstNameLabel}>
                        Annual income level (in USD $)*
                      </div>
                      <div className={styles.firstNameInput}>
                        <select
                          name="FinancialserviceProviders"
                          id="FinancialserviceProviders"
                          value={annualIncomeUser}
                          onChange={(e) => setAnnualIncomeUser(e.target.value)}
                        >
                          {annualIncome.map((income) => (
                            <option key={income.value}>{income.name}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div className={styles.firstName}>
                      <div className={styles.firstNameLabel}>
                        Government Tax ID number
                      </div>
                      <div className={styles.firstNameInput}>
                        <input
                          type="text"
                          id="emailOTP"
                          maxLength={260}
                          value={govtTaxId}
                          onChange={handleGovtTaxId}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  {/* email */}
                  {/* phn no */}

                  <div className={styles.formWrapper}>
                    <div className={styles.firstName}>
                      <div className={styles.firstNameLabel}>
                        Name of Primary Bank*
                      </div>
                      <div className={styles.firstNameInput}>
                        <input
                          type="text"
                          id="name_primary_bank"
                          maxLength={260}
                          value={primaryBank}
                          onChange={handlePrimaryBankname}
                          required
                        />
                      </div>
                    </div>
                    <div className={styles.firstName}>
                      <div className={styles.firstNameLabel}>
                        Financial service Providers*
                      </div>
                      <div className={styles.firstNameInput}>
                        <select
                          name="#"
                          id="#"
                          value={otherFinancialProviders}
                          onChange={(e) =>
                            setOtherFinancialProviders(e.target.value)
                          }
                          required
                        >
                          <option value="#">select other providers</option>
                          <option value="wise">Wise</option>
                          <option value="neteller">Neteller</option>
                          <option value="skrill">Skrill</option>
                          <option value="Western Union">Western Union</option>
                          <option value="PayPal">PayPal</option>
                          <option value="Xoom">Xoom</option>
                          <option value="Remitly">Remitly</option>
                          <option value="Binance">Binance</option>
                          <option value="Huobi">Huobi</option>
                          <option value="Paxful">Paxful</option>
                          <option value="Airtm">Airtm</option>
                          <option value="Other">Other</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  {/* NEW */}
                  <div className={styles.others}>
                    {otherFinancialProviders === "Other" ? (
                      <div className={styles.firstNameOther}>
                        <div className={styles.firstNameLabel}>
                          Input Other Financial Provider Name*
                        </div>
                        <div className={styles.firstNameInputOther}>
                          <input
                            type="text"
                            maxLength={260}
                            style={{
                              width: "100%",
                              background: "#f9f9f9",
                              border: "none",
                              outline: "none",
                            }}
                            onChange={handleOtherProvider}
                          />
                        </div>
                      </div>
                    ) : (
                      ""
                    )}
                  </div>
                  {/* NEW */}
                </form>
              </div>
              <div className={styles.submitBtnCont1}>
                <button
                  className={styles.submitBtn1}
                  onClick={handleKycAndDataFields}
                >
                  Complete your KYC here
                </button>
              </div>
            </div>
          </div>
        </div>
        <ToastContainer />
        <div className={styles.rightContainer}>
          <div className={styles.verificationGraphic}>
            <div className={styles.iconContainer}>
              <Icon icon="mdi:file-document-check-outline" width="120" height="120" />
            </div>
            <h3 className={styles.graphicTitle}>Financial Verification</h3>
            <p className={styles.graphicSubtitle}>
              Complete your financial details and KYC verification for secure transactions
            </p>
            <div className={styles.featureList}>
              <div className={styles.featureItem}>
                <Icon icon="mdi:bank" width="24" height="24" />
                <span>Banking Information</span>
              </div>
              <div className={styles.featureItem}>
                <Icon icon="mdi:shield-account" width="24" height="24" />
                <span>Identity Verification</span>
              </div>
              <div className={styles.featureItem}>
                <Icon icon="mdi:security" width="24" height="24" />
                <span>Secure Processing</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* ) : (
        <Login />
      )} */}
    </>
  );
};

export default Status;
