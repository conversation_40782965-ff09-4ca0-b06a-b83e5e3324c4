@font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

.mainContainer {
  width: 100%;
  height: auto;
  display: flex;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    flex-direction: column;
    max-width: 100%;
    overflow-x: hidden;
    height: auto;
    min-height: 100%;
  }

  @media screen and (min-width: 1500px) {
    height: auto;
    min-height: 100vh;
  }
}

.topContainer {
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
    display: block;
    position: relative;
  }

  @media screen and (min-width: 577px) {
    display: none;
  }
}

.leftContainer {
  width: 65%;
  display: flex;
  max-width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
    max-width: 100%;
  }
}

.leftWrapper {
  padding: 80px;
  width: 100%;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    padding: 30px 20px;
    max-width: 100%;
    overflow-x: hidden;
  }
}

.backBtn {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 50px;
  margin-bottom: 20px;
  font-size: 14px;

  @media screen and (max-width: 576px) {
    position: absolute;
    top: 10px;
    left: 14px;
  }
}

.backimg {
  margin-right: 10px;
  cursor: pointer;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.mainButtonsWrapper {
  display: flex;
  width: 100%;
  margin-bottom: 25px;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.personalBtn {
  background: linear-gradient(135deg, #4153ed 0%, #2196f3 100%);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 12px 20px;
  margin-right: 12px;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(65, 83, 237, 0.2);

  @media screen and (max-width: 576px) {
    padding: 10px 16px;
    font-size: 13px;
    margin-right: 8px;
  }
}

.employBtn {
  background: #ffffff;
  color: #6b7280;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  padding: 12px 20px;
  margin-right: 12px;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;

  @media screen and (max-width: 576px) {
    padding: 10px 16px;
    font-size: 13px;
    margin-right: 8px;
  }
}

.employBtn:hover {
  border-color: #4153ed;
  color: #4153ed;
}

.heading {
  color: #4153ed;
  font-family: Poppins;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  margin-bottom: 30px;
}

.formContainer {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: visible;
}

.formWrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  column-gap: 20px;
  max-width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    flex-direction: column;
    margin-bottom: 5px;
    column-gap: 0;
    max-width: 100%;
  }
}

/* Ensure each child in formWrapper takes same space */
.formWrapper > div {
  width: calc(50% - 10px);
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
  }
}

/* Override width for address field to make it full width */
.formWrapper > .addressName {
  width: 100% !important;
}

.firstName {
  width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
    margin: 10px 0px;
  }
}

.formWrapperField {
  width: 48%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  
  @media screen and (max-width: 576px) {
    width: 100%;
    margin-bottom: 10px;
  }
}

.firstNameLabel {
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 10px;
  word-wrap: break-word;

  @media screen and (max-width: 576px) {
    margin-bottom: 8px;
    font-size: 15px;
  }
}

.addressName {
  width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    margin-top: 10px;
  }
}

.lastName {
  width: 100%;
  margin-left: 20px;

  @media screen and (max-width : 576px) {
    width: 100%;
    margin-left: 0;
  }
}

.firstNameInput {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  background: #ffffff;
  border: 2px solid #e8eaed;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  min-width: 0;

  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  @media screen and (max-width: 576px) {
    width: 100%;
    height: 52px;
    border-radius: 10px;
  }
}

.firstNameInput:hover {
  border-color: #4153ed;
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.1);
}

.firstNameInput:focus-within {
  border-color: #4153ed;
  box-shadow: 0 0 0 3px rgba(65, 83, 237, 0.1);
}

.firstNameInput input {
  border: none;
  background-color: transparent;
  flex: 1;
  height: 100%;
  min-width: 0;
  padding: 0 16px;
  font-family: 'Poppins', sans-serif;
  font-size: 15px;
  font-weight: 400;
  color: #2d3748;
  box-sizing: border-box;
  outline: none;

  @media screen and (max-width: 576px) {
    font-size: 16px;
    padding: 0 14px;
  }
}

.firstNameInput input::placeholder {
  color: #a0aec0;
  font-weight: 300;
}

/* Add styles to ensure the phone input area has enough space */
.courtyOptions + input {
  flex: 1;
  min-width: 0;
}

.firstNameInputBusi {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  background: #ffffff;
  border: 2px solid #e8eaed;
  display: flex;
  box-sizing: border-box;

  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  @media screen and (max-width: 576px) {
    width: 100%;
    height: 52px;
    border-radius: 10px;
  }
}

.firstNameInputBusi:hover {
  border-color: #4153ed;
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.1);
}

.firstNameInputBusi:focus-within {
  border-color: #4153ed;
  box-shadow: 0 0 0 3px rgba(65, 83, 237, 0.1);
}

.firstNameInputBusi input {
  border: none;
  background-color: transparent;
  width: 100%;
  padding: 0 16px;
  font-family: 'Poppins', sans-serif;
  font-size: 15px;
  font-weight: 400;
  color: #2d3748;
  box-sizing: border-box;
  outline: none;

  @media screen and (max-width: 576px) {
    font-size: 16px;
    padding: 0 14px;
  }
}

.firstNameInputBusi input::placeholder {
  color: #a0aec0;
  font-weight: 300;
}

.firstNameInputSOF {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  background: #ffffff;
  border: 2px solid #e8eaed;
  display: flex;
  margin-bottom: 25px;
  box-sizing: border-box;

  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  @media screen and (max-width: 576px) {
    width: 100%;
    height: 52px;
    border-radius: 10px;
  }
}

.firstNameInputSOF:hover {
  border-color: #4153ed;
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.1);
}

.firstNameInputSOF:focus-within {
  border-color: #4153ed;
  box-shadow: 0 0 0 3px rgba(65, 83, 237, 0.1);
}

.firstNameInputBusi select,
.firstNameInput select,
.firstNameInputSOF select {
  border: none;
  background-color: transparent;
  width: 100%;
  padding: 0 16px;
  font-family: 'Poppins', sans-serif;
  font-size: 15px;
  font-weight: 400;
  color: #2d3748;
  box-sizing: border-box;
  outline: none;
  cursor: pointer;

  @media screen and (max-width: 576px) {
    font-size: 16px;
    padding: 0 14px;
  }
}

.lastNameInput {
  width: 100%;
  border: 1px solid black;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
  box-sizing: border-box;
}

.firstNameInputSOF select {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
}

.lastNameInput input {
  border: none;
  box-sizing: border-box;
  width: 100%;
}

.emailBtn {
  border-radius: 0px 5px 5px 0px;
  background: #140101;
  border: 1px solid #ebebeb;
  border-left: 2px solid #c4c3c3;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  font-family: 'Poppins';
  color: #f9f9f9;
  line-height: 100%;
  min-width: 70px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  @media screen and (max-width: 576px) {
    min-width: 70px;
    font-size: 11px;
    padding: 0 5px;
  }
}

.courtyOptions {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 120px;
  min-width: 120px;
  flex-shrink: 0;
  outline: none;
  border: none;
  border-right: 1px solid #c4c3c3;
  height: 100%;

  @media screen and (max-width: 576px) {
    width: 100px;
    min-width: 100px;
    height: 100%;
  }
}

.courtyOptions select {
  outline: none;
  border: none;
  width: 100%;
  background-color: #f9f9f9;
  padding: 8px 5px;
  height: 100%;

  @media screen and (max-width: 576px) {
    padding: 8px 2px;
    font-size: 12px;
  }
}

.calender {
  display: flex;
  justify-content: center;
  align-items: center;
}

.addressNameInput {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  background: #ffffff;
  border: 2px solid #e8eaed;
  box-sizing: border-box;
  max-width: 100%;

  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  @media screen and (max-width: 576px) {
    height: 52px;
    border-radius: 10px;
  }
}

.addressNameInput:hover {
  border-color: #4153ed;
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.1);
}

.addressNameInput:focus-within {
  border-color: #4153ed;
  box-shadow: 0 0 0 3px rgba(65, 83, 237, 0.1);
}

.addressNameInput input {
  border: none;
  background-color: transparent;
  width: 100%;
  height: 100%;
  padding: 0 16px;
  font-family: 'Poppins', sans-serif;
  font-size: 15px;
  font-weight: 400;
  color: #2d3748;
  box-sizing: border-box;
  outline: none;

  @media screen and (max-width: 576px) {
    font-size: 16px;
    padding: 0 14px;
  }
}

.addressNameInput input::placeholder {
  color: #a0aec0;
  font-weight: 300;
}

/* submitBtn */

.submitBtnCont {
  display: flex;
  justify-content: center;
  align-items: center;
}

.submitBtn {
  background: linear-gradient(135deg, #4153ed 0%, #2196f3 100%);
  border: none;
  padding: 0 48px;
  min-width: 160px;
  color: white;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  height: 52px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.2);
  outline: none;

  @media screen and (max-width: 576px) {
    width: 55%;
    padding: 0 20px;
    min-width: 140px;
    height: 48px;
    font-size: 15px;
    border-radius: 10px;
  }
}

.submitBtn:hover {
  background: linear-gradient(135deg, #3142dc 0%, #1976d2 100%);
  box-shadow: 0 6px 16px rgba(65, 83, 237, 0.3);
}

.submitBtn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(65, 83, 237, 0.2);
}

/* submitBtn */

/* submitBtn */

.submitBtnContEmp {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4 0px;
  margin-top: 50px;
}

.submitBtnEmp {
  background: linear-gradient(135deg, #4153ed 0%, #2196f3 100%);
  border: none;
  padding: 0 32px;
  color: white;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  height: 52px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.2);
  outline: none;

  @media screen and (max-width: 576px) {
    padding: 0 24px;
    height: 48px;
    font-size: 15px;
    border-radius: 10px;
  }
}

.submitBtnEmp:hover {
  background: linear-gradient(135deg, #3142dc 0%, #1976d2 100%);
  box-shadow: 0 6px 16px rgba(65, 83, 237, 0.3);
}

.submitBtnEmp:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(65, 83, 237, 0.2);
}

/* submitBtn */

.rightContainer {
  width: 35%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4153ed 100%);

  @media screen and (max-width: 576px) {
    display: none;
  }
}

/* Add a rule to make the image smaller on laptop screens */
.rightContainer .backimg {
  @media screen and (min-width: 577px) and (max-width: 1499px) {
    width: 500px !important;
    height: auto !important;
  }
}

.firstNameSOF {
  width: 100%;

  @media screen and (max-width : 576px) {
    width: 100%;
  }
}

/* Modern Mobile Header Styles */
.mobileHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  background: linear-gradient(135deg, #4153ed 0%, #2196f3 50%, #03a9f4 100%);
  color: white;
  text-align: center;
}

.verificationIcon {
  margin-bottom: 15px;
  color: white;
  opacity: 0.9;
}

.mobileTitle {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 500;
  margin: 0;
  color: white;
}

/* Modern Right Container Styles */
.verificationGraphic {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 40px;
  width: 100%;
  height: 100%;
  color: white;
}

.iconContainer {
  margin-bottom: 30px;
  opacity: 0.9;
}

.graphicTitle {
  font-family: 'Poppins', sans-serif;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 15px;
  color: white;
}

.graphicSubtitle {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 300;
  margin-bottom: 40px;
  line-height: 1.5;
  opacity: 0.9;
  max-width: 300px;
}

.featureList {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: flex-start;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: white;
  opacity: 0.9;
}

/* OTP Verification Styles */
.verifiedBadge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  margin-left: 12px;
  padding: 4px 12px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  font-size: 12px;
  font-weight: 500;
  border-radius: 20px;
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.2);

  @media screen and (max-width: 576px) {
    font-size: 11px;
    padding: 3px 10px;
    margin-left: 8px;
  }
}

.verifiedInput {
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%) !important;
  border-color: #10b981 !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

.verifiedInput input {
  background-color: transparent !important;
  color: #059669 !important;
  font-weight: 500 !important;
}

.verifiedIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  color: #10b981;

  @media screen and (max-width: 576px) {
    padding: 0 8px;
  }
}