@font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

.mainContainer {
  width: 100%;
  height: auto;
  display: flex;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    flex-direction: column;
    max-width: 100%;
    overflow-x: hidden;
    height: auto;
    min-height: 100%;
  }

  @media screen and (min-width: 1500px) {
    height: auto;
    min-height: 100vh;
  }
}

.topContainer {
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
    display: block;
    position: relative;
  }

  @media screen and (min-width: 577px) {
    display: none;
  }
}

.leftContainer {
  width: 65%;
  display: flex;
  max-width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
    max-width: 100%;
  }
}

.leftWrapper {
  padding: 80px;
  width: 100%;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    padding: 30px 20px;
    max-width: 100%;
    overflow-x: hidden;
  }
}

.backBtn {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 50px;
  margin-bottom: 20px;
  font-size: 14px;

  @media screen and (max-width: 576px) {
    position: absolute;
    top: 10px;
    left: 14px;
  }
}

.backimg {
  margin-right: 10px;
  cursor: pointer;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.mainButtonsWrapper {
  display: flex;
  width: 100%;
  margin-bottom: 25px;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.personalBtn {
  color: #4153ed;
  border: 1px solid #4153ed;
  border-radius: 5px;
  padding: 8px 10px 8px 10px;
  margin-right: 8px;
  font-size: 14px;
  cursor: pointer;
}

.employBtn {
  color: #000;
  border: 1px solid black;
  border-radius: 5px;
  padding: 8px 10px 8px 10px;
  margin-right: 8px;
  cursor: pointer;
}

.heading {
  color: #4153ed;
  font-family: Poppins;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  margin-bottom: 30px;
}

.formContainer {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: visible;
}

.formWrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  column-gap: 20px;
  max-width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    flex-direction: column;
    margin-bottom: 5px;
    column-gap: 0;
    max-width: 100%;
  }
}

/* Ensure each child in formWrapper takes same space */
.formWrapper > div {
  width: calc(50% - 10px);
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
  }
}

/* Override width for address field to make it full width */
.formWrapper > .addressName {
  width: 100% !important;
}

.firstName {
  width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
    margin: 10px 0px;
  }
}

.formWrapperField {
  width: 48%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  
  @media screen and (max-width: 576px) {
    width: 100%;
    margin-bottom: 10px;
  }
}

.firstNameLabel {
  font-size: 14px;
  margin-bottom: 8px;
  word-wrap: break-word;

  @media screen and (max-width: 576px) {
    margin-bottom: 5px;
  }
}

.addressName {
  width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    margin-top: 10px;
  }
}

.lastName {
  width: 100%;
  margin-left: 20px;

  @media screen and (max-width : 576px) {
    width: 100%;
    margin-left: 0;
  }
}

.firstNameInput {
  width: 100%;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  min-width: 0;

  @media screen and (max-width: 576px) {
    width: 100%;
    height: 45px;
  }
}

.firstNameInput input {
  border: none;
  background-color: #f9f9f9;
  flex: 1;
  height: 100%;
  min-width: 0;
  padding-left: 10px;
  padding-right: 10px;
  font-family: 'Poppins';
  font-size: 14px;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    margin-bottom: 5px;
  }
}

/* Add styles to ensure the phone input area has enough space */
.courtyOptions + input {
  flex: 1;
  min-width: 0;
}

.firstNameInputBusi {
  width: 100%;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
  display: flex;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
  }
}

.firstNameInputBusi input {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    margin-bottom: 5px;
  }
}

.firstNameInputSOF {
  width: 100%;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
  display: flex;
  margin-bottom: 25px;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
  }
}

.firstNameInputBusi select,
.firstNameInput select,
.firstNameInputSOF select {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
  box-sizing: border-box;
}

.lastNameInput {
  width: 100%;
  border: 1px solid black;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
  box-sizing: border-box;
}

.firstNameInputSOF select {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
}

.lastNameInput input {
  border: none;
  box-sizing: border-box;
  width: 100%;
}

.emailBtn {
  border-radius: 0px 5px 5px 0px;
  background: #140101;
  border: 1px solid #ebebeb;
  border-left: 2px solid #c4c3c3;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  font-family: 'Poppins';
  color: #f9f9f9;
  line-height: 100%;
  min-width: 70px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  @media screen and (max-width: 576px) {
    min-width: 70px;
    font-size: 11px;
    padding: 0 5px;
  }
}

.courtyOptions {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 120px;
  min-width: 120px;
  flex-shrink: 0;
  outline: none;
  border: none;
  border-right: 1px solid #c4c3c3;
  height: 100%;

  @media screen and (max-width: 576px) {
    width: 100px;
    min-width: 100px;
    height: 100%;
  }
}

.courtyOptions select {
  outline: none;
  border: none;
  width: 100%;
  background-color: #f9f9f9;
  padding: 8px 5px;
  height: 100%;

  @media screen and (max-width: 576px) {
    padding: 8px 2px;
    font-size: 12px;
  }
}

.calender {
  display: flex;
  justify-content: center;
  align-items: center;
}

.addressNameInput {
  width: 100%;
  height: 40px;
  box-sizing: border-box;
  max-width: 100%;
}

.addressNameInput input {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  box-sizing: border-box;
}

/* submitBtn */

.submitBtnCont {
  display: flex;
  justify-content: center;
  align-items: center;
}

.submitBtn {
  background-color: #4153ed;
  padding: 0px 50px;
  gap: 10px;
  color: white;
  border-color: #4153ed;
  outline: none;
  border-radius: 3px;
  height: 45px;
  cursor: pointer;

  @media screen and (max-width : 576px) {
    padding: 0px 0px;
    width: 45%;
  }
}

/* submitBtn */

/* submitBtn */

.submitBtnContEmp {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4 0px;
  margin-top: 50px;
}

.submitBtnEmp {
  background-color: #4153ed;
  padding: 0px 50px;
  gap: 10px;
  color: white;
  border-color: #4153ed;
  outline: none;
  border-radius: 3px;
  height: 45px;
}

/* submitBtn */

.rightContainer {
  width: 35%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fb;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

/* Add a rule to make the image smaller on laptop screens */
.rightContainer .backimg {
  @media screen and (min-width: 577px) and (max-width: 1499px) {
    width: 500px !important;
    height: auto !important;
  }
}

.firstNameSOF {
  width: 100%;

  @media screen and (max-width : 576px) {
    width: 100%;
  }
}

/* Modern Mobile Header Styles */
.mobileHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.verificationIcon {
  margin-bottom: 15px;
  color: white;
  opacity: 0.9;
}

.mobileTitle {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 500;
  margin: 0;
  color: white;
}

/* Modern Right Container Styles */
.verificationGraphic {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 40px;
  height: 100%;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.iconContainer {
  margin-bottom: 30px;
  opacity: 0.9;
}

.graphicTitle {
  font-family: 'Poppins', sans-serif;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 15px;
  color: white;
}

.graphicSubtitle {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 300;
  margin-bottom: 40px;
  line-height: 1.5;
  opacity: 0.9;
  max-width: 300px;
}

.featureList {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: flex-start;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: white;
  opacity: 0.9;
}